import requests
import json

url = 'https://api-inference.modelscope.cn/v1/images/generations'

payload = {
    'model': 'movietalk/jimeng',
    'prompt': 'A simple cat sitting on a chair'  # 简化的提示词
}

headers = {
    'Authorization': 'Bearer ms-58a23aa6-ed83-4617-a9d9-1540b24b7dda',
    'Content-Type': 'application/json'
}

try:
    print("正在发送API请求...")
    response = requests.post(url, data=json.dumps(payload, ensure_ascii=False).encode('utf-8'), headers=headers, timeout=30)
    
    print(f"状态码: {response.status_code}")
    print(f"响应头: {dict(response.headers)}")
    
    if response.status_code == 200:
        response_data = response.json()
        print(f"API调用成功!")
        print(f"响应数据: {response_data}")
    else:
        print(f"API调用失败，状态码: {response.status_code}")
        print(f"错误信息: {response.text}")
        
except requests.exceptions.Timeout:
    print("请求超时，可能是网络连接问题")
except requests.exceptions.ConnectionError:
    print("连接错误，请检查网络连接")
except Exception as e:
    print(f"发生错误: {str(e)}")
