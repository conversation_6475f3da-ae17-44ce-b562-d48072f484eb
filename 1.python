import requests
import json
from PIL import Image
from io import BytesIO

url = 'https://api-inference.modelscope.cn/v1/images/generations'

payload = {
    'model': 'movietalk/jimeng',#ModelScope Model-Id,required
    'prompt': 'A low-angle, full-body candid photograph of a lone cyborg standing in a rain-slicked alleyway of a sprawling cyberpunk metropolis. Towering skyscrapers with massive, glowing holographic advertisements pierce the polluted, perpetually twilight sky. Neon signs in Japanese and English cast vibrant, realistic pink and cyan reflections on the wet asphalt. The cyborg, wearing a worn leather jacket with subtle, integrated glowing wires, looks pensively into a puddle, her own reflection staring back with a single glowing cybernetic eye. Steam rises from a nearby street vent, caught in the dramatic, moody lighting, photorealistic, hyperrealistic, street photography, shot on a Sony A7R IV with a 35mm f/1.4 lens, moody noir atmosphere, ultra-detailed, sharp focus, cinematic lighting, realistic reflections on wet ground, volumetric steam, 8K, RAW photo.'# required
}
headers = {
    'Authorization': 'Bearer ms-58a23aa6-ed83-4617-a9d9-1540b24b7dda',
    'Content-Type': 'application/json'
}

response = requests.post(url, data=json.dumps(payload, ensure_ascii=False).encode('utf-8'), headers=headers)

response_data = response.json()
image = Image.open(BytesIO(requests.get(response_data['images'][0]['url']).content))
image.save('result_image.jpg')